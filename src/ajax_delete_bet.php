<?php
// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Set content type to JSON
header('Content-Type: application/json');

global $conexion;

use App\classes\PartidoBet;

require_once '../config/config.php';
require_once __ROOT__ . '/src/query/conexion.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/classes/usuario.php';
require_once __ROOT__ . '/src/general/preparar.php';

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'message' => 'Method not allowed'
    ]);
    exit;
}

try {
    // Validate required parameters
    if (!isset($_POST['bet_id']) || empty(trim($_POST['bet_id']))) {
        throw new Exception('ID de apuesta requerido');
    }

    $betId = trim($_POST['bet_id']);

    // Check if bet exists before attempting to delete
    $bet = PartidoBet::get($betId, $conexion);
    if ($bet === null) {
        throw new Exception('La apuesta no existe o ya fue eliminada');
    }

    // Check if bet is already deleted (estado = 0)
    if ($bet->getEstado() === 0) {
        throw new Exception('La apuesta ya fue eliminada anteriormente');
    }

    // Delete the bet (soft delete - sets estado = 0)
    $success = PartidoBet::delete($betId, $conexion);

    if ($success) {
        echo json_encode([
            'success' => true,
            'message' => 'Apuesta eliminada exitosamente'
        ]);
    } else {
        throw new Exception('Error al eliminar la apuesta');
    }

} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
