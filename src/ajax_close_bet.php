<?php
// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Set content type to JSON
header('Content-Type: application/json');

global $conexion;

use App\classes\PartidoBet;

require_once '../config/config.php';
require_once __ROOT__ . '/src/query/conexion.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/classes/usuario.php';
require_once __ROOT__ . '/src/general/preparar.php';

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'message' => 'Method not allowed'
    ]);
    exit;
}

try {
    // Validate required parameters
    if (!isset($_POST['bet_id']) || empty(trim($_POST['bet_id']))) {
        throw new Exception('ID de apuesta requerido');
    }

    if (!isset($_POST['result']) || !in_array($_POST['result'], ['won', 'lost'])) {
        throw new Exception('Resultado de apuesta inválido');
    }

    $betId = trim($_POST['bet_id']);
    $isWon = ($_POST['result'] === 'won');

    // Close the bet
    $success = PartidoBet::closeBet($betId, $isWon, $conexion);

    if ($success) {
        echo json_encode([
            'success' => true,
            'message' => 'Apuesta cerrada exitosamente',
            'result' => $_POST['result']
        ]);
    } else {
        throw new Exception('Error al cerrar la apuesta');
    }

} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
