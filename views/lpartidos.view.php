<?php
#region region DOCS
/** @var Partido[] $partidos */
/** @var Partido[] $torneos */
/** @var string $matchup */
/** @var string $fecha */
/** @var int $solo_odds_por_revisar */
#endregion docs
?>
<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
    <meta charset="utf-8"/>
    <title>My Dash | Partidos</title>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
    <meta content="" name="description"/>
    <meta content="" name="author"/>
    
    <?php require_once __ROOT__ . '/views/head.view.php';?>
    <link id="region_CSS_date" href="<?php echo RUTA ?>resources/assets/plugins/bootstrap-datepicker/dist/css/bootstrap-datepicker.css" rel="stylesheet"/>
    <?php #region region CSS autocomplete ?>
    <link rel="stylesheet" href="<?php echo RUTA ?>resources/choices.js/choices.min.css">
    <link rel="stylesheet" href="<?php echo RUTA ?>resources/choices.js/fab_choices.css">
    <?php #endregion CSS autocomplete ?>
<style>
	#mdl_home_team, #mdl_away_team{
		margin-top: 15px;
	}

	/* Enhanced Fixture Column Styling */
	.fixture-container {
		min-height: 50px;
		padding: 4px;
	}

	.team-badge-container {
		flex: 1;
		max-width: 120px;
	}

	.team-name-badge {
		transition: all 0.3s ease;
		border: 1px solid rgba(255, 255, 255, 0.1);
		box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
		max-width: 100%;
		display: inline-flex;
		align-items: center;
	}

	.team-name-badge:hover {
		transform: translateY(-1px);
		box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
		border-color: rgba(255, 255, 255, 0.3);
	}

	.team-name-badge .team-text {
		max-width: 80px;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}

	.vs-separator {
		flex-shrink: 0;
	}

	.vs-separator .badge {
		transition: all 0.3s ease;
		border: 1px solid rgba(255, 255, 255, 0.2);
	}

	.vs-separator .badge:hover {
		transform: scale(1.1);
		background-color: #495057 !important;
	}

	.tournament-info .badge {
		transition: all 0.3s ease;
		border: 1px solid rgba(255, 255, 255, 0.1);
	}

	.tournament-info .badge:hover {
		background-color: #6c757d !important;
		border-color: rgba(255, 255, 255, 0.3);
	}

	/* Responsive adjustments */
	@media (max-width: 768px) {
		.team-name-badge .team-text {
			max-width: 60px;
		}

		.fixture-container {
			min-height: 45px;
		}
	}
</style>
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
    <span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed app-without-sidebar app-with-top-menu">
    <!-- #header -->
    <?php require_once __ROOT__ . '/views/header.view.php'; ?>

    <!-- #sidebar -->
    <?php require_once __ROOT__ . '/views/topbar.view.php'; ?>

    <!-- BEGIN #content -->
    <div id="content" class="app-content">
        <!-- FORM -->
        <form action="lpartidos" class="region_FORM" method="POST">
            <input type="hidden" id="selidpartido" name="selidpartido">
            <input type="hidden" id="tabselected" name="tabselected" value="<?php echo limpiar_datos($tabselected); ?>">

            <?php #region region filtros ?>
            <!-- BEGIN ROW -->
            <div class="row">
                <!-- BEGIN text -->
                <div class="col-md-8 col-xs-12">
                    <label for="matchup" class="d-flex align-items-center fs-12px">
                        Matchup:
                    </label>
                    <input type="text" class="form-control form-control-fh text-uppercase fs-12px no-border-radious" name="matchup" id="matchup" value="<?php echo @recover_var($matchup) ?>" autofocus onclick="this.focus();this.select('');"/>
                </div>
                <!-- END text -->
                <!-- BEGIN date -->
                <div class="col-md-4 col-xs-12">
                    <label for="fecha" class="d-flex align-items-center fs-12px">
                        Fecha:
                    </label>
                    <div class="input-group">
                        <input type="text" class="form-control form-control-fh fs-12px no-border-radious datepicker" id="fecha" name="fecha" value="<?php echo @recover_var($fecha) ?>" autocomplete="off"/>
                        <span class="input-group-text no-border-radious mouse-pointer" onclick="region_JS_sub_clearfecha();">
                            <i class="fa fa-xmark fa-md cursor-pointer"></i>
                        </span>
                    </div>
                </div>
                <!-- END date -->
            </div>
            <!-- END ROW -->
            <!-- BEGIN ROW torneo filter -->
            <div class="row mt-2">
                <!-- BEGIN select torneo -->
                <div class="col-md-12 col-xs-12">
                    <label for="torneo" class="d-flex align-items-center fs-12px">
                        Torneo:
                    </label>
                    <select id="torneo" name="torneo" class="form-select fs-12px no-border-radious">
                        <option value="">-- Todos los torneos --</option>
                        <?php foreach ($torneos as $torneo_option): ?>
                            <option value="<?php echo htmlspecialchars($torneo_option->pais); ?>" <?php echo (@recover_var($torneo_filter) == $torneo_option->pais) ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($torneo_option->pais); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <!-- END select torneo -->
            </div>
            <!-- END ROW torneo filter -->
            <!-- BEGIN row -->
            <div class="row mt-3">
                <!-- BEGIN row -->
                <div class="row">
                    <!-- BEGIN switch -->
                    <div class="col-md-3 col-xs-12">
                        <div class="form-check form-switch mb-2">
                            <input class="form-check-input no-border-radious" type="checkbox" id="solo_odds_por_revisar" name="solo_odds_por_revisar" <?php echo @recoverVarCheckbox($solo_odds_por_revisar); ?>>
                            <label class="form-check-label fs-14px" for="solo_odds_por_revisar">
                                Solo con odds por revisar
                            </label>
                        </div>
                    </div>
                    <!-- END switch -->
                    <!-- BEGIN switch checkbox -->
                    <div class="col-md-3 col-xs-12">
                        <div class="form-check form-switch mb-2">
                            <input class="form-check-input no-border-radious" type="checkbox" id="solo_por_jugar" name="solo_por_jugar" <?php echo @recoverVarCheckbox($solo_por_jugar); ?>>
                            <label class="form-check-label fs-14px" for="solo_por_jugar">
                                Solo por jugar
                            </label>
                        </div>
                    </div>
                    <!-- END switch checkbox -->
                </div>
                <!-- END row -->
            </div>
            <!-- END row -->
            <?php #endregion filtros ?>
            <!-- ROW -->
            <div class="row">
                <?php #region region SUBMIT sub_search ?>
                <div class="col-md-3 col-xs-12">
                    <button type="submit" id="sub_search" name="sub_search" class="region_SUBMIT_sub_search btn btn-xs btn-primary w-100 no-border-radious">
                        Buscar
                    </button>
                </div>
                <?php #endregion SUBMIT sub_search ?>
	            <!-- LINK -->
	            <div class="col-md-3 col-xs-12">
		            <a href="#mdl_eliminar_partidosporrevisar_hasta" data-bs-toggle="modal" class="btn btn-xs btn-danger w-100 no-border-radious" >
			            Eliminar hasta
		            </a>
	            </div>
	            <!-- END LINK -->
	            <?php #region region LINK ver probabilidades ?>
                <div class="col-md-3 col-xs-12">
                    <a href="lpartidos_probabilidades" class="btn btn-xs btn-default w-100 no-border-radious">
                        Ver listado de probabilidades
                    </a>
                </div>
	            <?php #endregion LINK ver probabilidades ?>
                <!-- LINK -->
                <div class="col-md-3 col-xs-12">
                    <a href="ipartido" class="btn btn-xs btn-success w-100 no-border-radious">
                        Crear
                    </a>
                </div>
                <!-- END LINK -->
            </div>
            <!-- END ROW -->
            <!-- BEGIN navtab -->
            <ul class="region_NAVTAB_HEAD nav nav-tabs fs-13px mt-3 no-border-radious">
                <li class="nav-item" onclick="tabselect(1)">
                    <a href="#default-tab-1" data-bs-toggle="tab" class="nav-link <?php echo ($tabselected == 1) ? "active" : ""; ?>">
                        Partidos
                        <span class="badge bg-primary rounded-0 fs-10px ms-1">
                            <?php echo count($partidos); ?>
                        </span>
                    </a>
                </li>
                <li class="nav-item" onclick="tabselect(2)">
                    <a href="#default-tab-2" data-bs-toggle="tab" class="nav-link <?php echo ($tabselected == 2) ? "active" : ""; ?>">
                        Torneos
                        <span class="badge bg-primary rounded-0 fs-10px ms-1">
                            <?php echo count($torneos); ?>
                        </span>
                    </a>
                </li>
            </ul>
            <div class="tab-content panel rounded-0 rounded-bottom" style="overflow: auto; height: 400px">
                <!-- BEGIN NAVTAB1 partidos -->
                <div class="region_NAVTAB_partidos tab-pane fade <?php echo ($tabselected == 1) ? "active show" : ""; ?>" id="default-tab-1">
                    <?php #region region TABLE partidos ?>
                    <table id="table_partidos" class="table table-hover table-sm">
                        <thead>
                        <tr>
                            <th></th>
                            <th class="text-center">Fecha</th>
                            <th class="text-center">Fixture</th>
                            <th class="text-center">Pais</th>
                        </tr>
                        </thead>
                        <tbody class="fs-10px">
                        <?php foreach ($partidos as $partido): ?>
                            <tr class          = "cursor-pointer"
                                data-bs-toggle = "modal"
                                data-bs-target = "#mdl_review_partido"
                                data-idpartido = "<?php echo limpiar_datos($partido->id) ?>"
                                data-matchup   = "<?php echo $partido->home . ' -VS- ' . $partido->away ?>"
                                data-home      = "<?php echo $partido->home ?>"
                                data-away      = "<?php echo $partido->away ?>"
                                data-torneo    = "<?php echo $partido->pais ?>"
                                data-fecha     = "<?php echo $partido->fecha ?>"
                                data-hora      = "<?php echo $partido->horamilitar ?>">
                                <td class="align-middle">
	                                <?php if ($partido->revisado_probabilidades == 1): ?>
                                        <i class="fas fa-check-circle fa-1_5x text-success"></i>
	                                <?php endif; ?>
                                </td>
                                <td class="text-center">
                                    <div><?php echo $partido->fechadiashora; ?></div>
                                    <?php if (!empty($partido->fecha) && !empty($partido->horamilitar)): ?>
                                        <div class="countdown-display fs-10px text-info"
                                             data-match-date="<?php echo $partido->fecha; ?>"
                                             data-match-time="<?php echo $partido->horamilitar; ?>"
                                             id="countdown-<?php echo $partido->id; ?>">
                                            <i class="fas fa-clock me-1"></i>
                                            <span class="countdown-text">Calculating...</span>
                                        </div>
                                    <?php endif; ?>
                                </td>
                                <td class="<?php echo $partido->bgcolor; ?> text-center align-middle">
                                    <!-- Enhanced Fixture Display -->
                                    <div class="fixture-container d-flex flex-column align-items-center">
                                        <!-- Teams Row -->
                                        <div class="d-flex align-items-center justify-content-center w-100 mb-1">
                                            <!-- Home Team -->
                                            <div class="team-badge-container me-2">
                                                <span class="badge bg-primary rounded-pill fs-10px px-2 py-1 cursor-pointer team-name-badge"
                                                      id="home_<?php echo $partido->id; ?>"
                                                      onclick="copyAndShowTooltip('home_<?php echo $partido->id; ?>')"
                                                      title="<?php echo htmlspecialchars($partido->home); ?> - Click to copy">
                                                    <i class="fas fa-home me-1"></i>
                                                    <span class="team-text"><?php echo htmlspecialchars($partido->home); ?></span>
                                                </span>
                                            </div>

                                            <!-- VS Separator -->
                                            <div class="vs-separator mx-1">
                                                <span class="badge bg-dark rounded-circle d-inline-flex align-items-center justify-content-center"
                                                      style="width: 24px; height: 24px; font-size: 8px;">
                                                    VS
                                                </span>
                                            </div>

                                            <!-- Away Team -->
                                            <div class="team-badge-container ms-2">
                                                <span class="badge bg-warning text-dark rounded-pill fs-10px px-2 py-1 cursor-pointer team-name-badge"
                                                      id="away_<?php echo $partido->id; ?>"
                                                      onclick="copyAndShowTooltip('away_<?php echo $partido->id; ?>')"
                                                      title="<?php echo htmlspecialchars($partido->away); ?> - Click to copy">
                                                    <span class="team-text"><?php echo htmlspecialchars($partido->away); ?></span>
                                                    <i class="fas fa-plane ms-1"></i>
                                                </span>
                                            </div>
                                        </div>

                                        <!-- Tournament Badge -->
                                        <div class="tournament-info">
                                            <span class="badge bg-secondary rounded-0 fs-9px px-2 py-1">
                                                <i class="fas fa-trophy me-1 text-success"></i>
                                                <?php echo htmlspecialchars($partido->pais); ?>
                                            </span>
                                        </div>
                                    </div>
                                </td>
                                <td class="text-start align-middle">
                                    <span class="fs-12px"><?php echo $partido->pais; ?></span>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                        </tbody>
                    </table>
                    <?php #endregion TABLE partidos ?>
                </div>
                <!-- END NAVTAB1 partidos -->
                <!-- BEGIN NAVTAB2 torneos -->
                <div class="region_NAVTAB_torneos tab-pane fade <?php echo ($tabselected == 2) ? "active show" : ""; ?>" id="default-tab-2">
                    <?php #region region TABLE torneos ?>
                    <table id="table_torneos" class="table table-hover table-sm">
                        <thead>
                        <tr>
                            <th class="text-center">Nombre</th>
                            <th class="text-center">Fecha ultimo partido</th>
                            <th class="text-center">Numero partidos</th>
                        </tr>
                        </thead>
                        <tbody class="fs-10px">
                        <?php foreach ($torneos as $torneo): ?>
                            <tr class="cursor-pointer">
                                <td><?php echo $torneo->pais; ?></td>
                                <td class="text-center"><?php echo $torneo->max_fecha_partido_info; ?></td>
                                <td class="text-center"><?php echo $torneo->numero_partidos; ?></td>
                            </tr>
                        <?php endforeach; ?>
                        </tbody>
                    </table>
                    <?php #endregion TABLE torneos ?>
                </div>
                <!-- END NAVTAB2 torneos -->
            </div>
            <!-- END navtab -->
            <?php #region region MODAL mdl_review_partido ?>
            <div class="modal fade" id="mdl_review_partido">
                <div class="modal-dialog modal-lg modal-dialog-centered">
                    <div class="modal-content">
                        <input type="hidden" id="mdl_review_partido_idpartido" name="mdl_review_partido_idpartido">

                        <div class="modal-header">
                            <h4 class="modal-title">Revisar partido</h4>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-hidden="true"></button>
                        </div>
                        <div class="modal-body">
                            <!-- BEGIN Enhanced Match Information Card -->
                            <div class="card bg-dark border-secondary mb-3">
                                <div class="card-body p-3">
                                    <!-- Match Teams Row -->
                                    <div class="row align-items-center mb-3">
                                        <div class="col-md-5">
                                            <div class="text-center">
                                                <div class="badge bg-primary mb-2 fs-10px">HOME</div>
                                                <h5 class="text-white mb-0 fw-bold cursor-pointer"
                                                    id="mdl_home_team"
                                                    onclick="copyAndShowTooltip('mdl_home_team')"
                                                    title="Click to copy">
                                                    <i class="fas fa-home me-2 text-primary"></i>
                                                    <span id="mdl_home_team_text"></span>
                                                </h5>
                                            </div>
                                        </div>
                                        <div class="col-md-2">
                                            <div class="text-center">
                                                <div class="vs-circle rounded-circle d-inline-flex align-items-center justify-content-center bg-secondary"
                                                     style="width: 40px; height: 40px;">
                                                    <span class="text-white fw-bold fs-14px">VS</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-5">
                                            <div class="text-center">
                                                <div class="badge bg-warning mb-2 fs-10px">AWAY</div>
                                                <h5 class="text-white mb-0 fw-bold cursor-pointer"
                                                    id="mdl_away_team"
                                                    onclick="copyAndShowTooltip('mdl_away_team')"
                                                    title="Click to copy">
                                                    <span id="mdl_away_team_text"></span>
                                                    <i class="fas fa-plane ms-2 text-warning"></i>
                                                </h5>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Match Details Row -->
                                    <div class="row">
                                        <div class="col-md-6 mb-3 mb-md-0">
                                            <div class="d-flex align-items-center bg-secondary rounded p-2">
                                                <i class="fas fa-trophy text-success me-3 fs-16px"></i>
                                                <div>
                                                    <small class="text-muted d-block">Tournament</small>
                                                    <span class="text-white fw-semibold" id="mdl_tournament_text"></span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="d-flex align-items-center bg-secondary rounded p-2">
                                                <i class="fas fa-calendar-alt text-info me-3 fs-16px"></i>
                                                <div class="flex-grow-1">
                                                    <small class="text-muted d-block">Match Date & Time</small>
                                                    <span class="text-white fw-semibold" id="mdl_datetime_text"></span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- END Enhanced Match Information Card -->
                            <!-- BEGIN row -->
                            <div class="row mt-3">
	                            <?php #region region SUBMIT sub_ver_probabilidades ?>
                                <div class="col-md-12 col-xs-12">
                                    <button type="submit" id="sub_ver_probabilidades" name="sub_ver_probabilidades" class="btn btn-xs btn-primary w-100 no-border-radious">
                                        Ver probabilidades
                                    </button>
                                </div>
	                            <?php #endregion SUBMIT sub_ver_probabilidades ?>
                            </div>
                            <!-- END row -->
                            <!-- BEGIN row -->
                            <div class="row mt-3">
                                <?php #region region SUBMIT sub_verodds ?>
                                <div class="col-md-12 col-xs-12">
                                    <button type="submit" id="sub_verodds" name="sub_verodds" class="btn btn-xs btn-primary w-100 no-border-radious">
                                        Ver odds
                                    </button>
                                </div>
                                <?php #endregion SUBMIT sub_verodds ?>
                            </div>
                            <!-- END row -->
                            <!-- BEGIN row -->
                            <div class="row mt-3">
                                <?php #region region SUBMIT sub_verinfo ?>
                                <div class="col-md-12 col-xs-12">
                                    <button type="submit" id="sub_verinfo" name="sub_verinfo" class="btn btn-xs btn-primary no-border-radious w-100">
                                        Ver informacion
                                    </button>
                                </div>
                                <?php #endregion SUBMIT sub_verinfo ?>
                            </div>
                            <!-- END row -->
                            <!-- BEGIN row -->
                            <div class="row mt-3">
                                <?php #region region SUBMIT sub_editpartido ?>
                                <div class="col-md-12 col-xs-12">
                                    <button type="submit" id="sub_editpartido" name="sub_editpartido" class="btn btn-xs btn-success no-border-radious w-100">
                                        Editar
                                    </button>
                                </div>
                                <?php #endregion SUBMIT sub_editpartido ?>
                            </div>
                            <!-- END row -->
                            <!-- BEGIN row -->
                            <div class="row mt-3">
                                <?php #region region SUBMIT sub_delpartido ?>
                                <div class="col-md-12 col-xs-12">
                                    <button type="submit" id="sub_delpartido" name="sub_delpartido" class="btn btn-xs btn-danger no-border-radious w-100">
                                        Eliminar
                                    </button>
                                </div>
                                <?php #endregion SUBMIT sub_delpartido ?>
                            </div>
                            <!-- END row -->
                        </div>
                        <div class="modal-footer">
                            <a href="#" class="btn btn-xs btn-default no-border-radious" data-bs-dismiss="modal">
                                Cancelar
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <?php #endregion MODAL mdl_review_partido ?>
	        <?php #region region MODAL mdl_eliminar_partidosporrevisar_hasta ?>
	        <div class="modal fade" id="mdl_eliminar_partidosporrevisar_hasta">
		        <div class="modal-dialog modal-lg modal-dialog-centered">
			        <div class="modal-content">
				        <div class="modal-header">
					        <h4 class="modal-title">Eliminar partidos</h4>
					        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-hidden="true"></button>
				        </div>
				        <div class="modal-body">
					        <!-- BEGIN text -->
					        <div class="col-md-12 col-xs-12">
						        <div class="input-group">
							        <span class="input-group-text no-border-radious bg-gray fs-11px w-100px">
							            Hora militar:
							        </span>
							        <input type="text" name="eliminar_partidosporrevisar_hasta_hora" id="eliminar_partidosporrevisar_hasta_hora" class="form-control form-control-fh fs-12px no-border-radious "/>
						        </div>
					        </div>
					        <!-- END text -->
				        </div>
				        <div class="modal-footer">
					        <a href="#" class="btn btn-white" data-bs-dismiss="modal">
						        Cancelar
					        </a>
					        <button type="submit" id="sub_eliminar_partidosporrevisar_hasta" name="sub_eliminar_partidosporrevisar_hasta" class="btn btn-danger no-border-radious">
						        Eliminar
					        </button>
				        </div>
			        </div>
		        </div>
	        </div>
	        <?php #endregion MODAL mdl_eliminar_partidosporrevisar_hasta ?>
        </form>
        <!-- END FORM -->
    </div>
    <!-- END #content -->

    <!-- BEGIN scroll-top-btn -->
    <a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
    <!-- END scroll-top-btn -->
</div>
<!-- END #app -->


<?php #region region JS ?>
<?php require_once __ROOT__ . '/views/js_core.view.php'; ?>

<?php #region region JS datepicker ?>
<script src="<?php echo RUTA ?>resources/assets/plugins/bootstrap-datepicker/dist/js/bootstrap-datepicker.js"></script>
<script src="<?php echo RUTA ?>resources/js/datepickerini.js"></script>
<?php #endregion JS datepicker ?>
<?php #region region JS date fecha submit form when date changed ?>
<script>
    $(document).ready(function () {
        // Initialize the Bootstrap datepicker
        $('#fecha').datepicker({
                format        : "yyyy-mm-dd",
                todayHighlight: true,
                autoclose     : true
            })
            .on('changeDate', function (e) {
                // Submit the form when the date is changed
                $(this).closest('form').submit();
            });
    });
</script>
<?php #endregion JS date fecha submit form when date changed ?>
<!-- BEGIN region_JS_editpartido -->
<script type="text/javascript">
    function region_JS_editpartido($idbudget) {
        const selidpartido = document.getElementById('selidpartido');
        selidpartido.value = $idbudget;
        
        document.getElementById('sub_editpartido').click();
    }
</script>
<!-- END JS_editpartido -->
<?php #region region JS press enter submit matchup ?>
<script id="region_JS_pressentersubmit" type="text/javascript">
    var input = document.getElementById("matchup");
    
    input.addEventListener("keypress", function (event) {
        
        if (event.key === "Enter") {
            event.preventDefault();
            document.getElementById("sub_search").click();
        }
    });
</script>
<?php #endregion press enter submit matchup ?>
<?php #region region JS clear fecha ?>
<script type="text/javascript">
    function region_JS_sub_clearfecha() {
        document.getElementById('fecha').value = '';
    }
</script>
<?php #endregion JS clear fecha ?>
<?php #region region JS MODAL mdl_review_partido ?>
<script id="region_region_js_modal_mdl_review_partido" type="text/javascript">
    $('#mdl_review_partido').on('shown.bs.modal', function (event) {
        const button              = $(event.relatedTarget);
        const recipient_idpartido = button.data('idpartido');
        const recipient_matchup   = button.data('matchup');
        const recipient_home      = button.data('home');
        const recipient_away      = button.data('away');
        const recipient_torneo    = button.data('torneo');
        const recipient_fecha     = button.data('fecha');
        const recipient_hora      = button.data('hora');

        // Set hidden field for form submission
        const mdl_review_partido_idpartido = document.getElementById('mdl_review_partido_idpartido');
        mdl_review_partido_idpartido.value = recipient_idpartido;

        // Populate enhanced modal fields
        const mdl_home_team_text = document.getElementById('mdl_home_team_text');
        const mdl_away_team_text = document.getElementById('mdl_away_team_text');
        const mdl_tournament_text = document.getElementById('mdl_tournament_text');
        const mdl_datetime_text = document.getElementById('mdl_datetime_text');

        // Set team names for display and copying
        mdl_home_team_text.textContent = recipient_home;
        mdl_away_team_text.textContent = recipient_away;
        mdl_tournament_text.textContent = recipient_torneo;

        // Format and display date/time
        if (recipient_fecha && recipient_hora) {
            const formattedDateTime = recipient_fecha + ' ' + recipient_hora;
            mdl_datetime_text.textContent = formattedDateTime;
        } else {
            mdl_datetime_text.textContent = 'Date/Time not available';
        }

        // Set the text content for the clickable elements (for copying)
        document.getElementById('mdl_home_team').textContent = recipient_home;
        document.getElementById('mdl_away_team').textContent = recipient_away;
    })
</script>
<?php #endregion JS MODAL mdl_review_partido ?>
<!-- BEGIN JS tabselect -->
<script id="region_JS_tabselect" type="text/javascript">
    function tabselect(ntab) {
        document.getElementById('tabselected').value = ntab;
    }
</script>
<!-- END JS tabselect -->
<?php #region region JS datatable ?>
<script src="<?php echo RUTA ?>resources/assets/plugins/datatables.net/js/jquery.dataTables.min.js"></script>
<script src="<?php echo RUTA ?>resources/assets/plugins/datatables.net-bs5/js/dataTables.bootstrap5.min.js"></script>
<script src="<?php echo RUTA ?>resources/assets/plugins/datatables.net-responsive/js/dataTables.responsive.min.js"></script>
<script src="<?php echo RUTA ?>resources/assets/plugins/datatables.net-responsive-bs5/js/responsive.bootstrap5.min.js"></script>
<script>
    $(document).ready(function () {
        // table_partidos is now a regular HTML table without DataTable functionality
        
        // Initialize table_torneos only when tab is shown
        $('a[href="#default-tab-2"]').on('shown.bs.tab', function () {
            if (!$.fn.DataTable.isDataTable('#table_torneos')) {
                $('#table_torneos').DataTable({
                    responsive: true,
                    language  : {
                        search: "Buscar:",
                    },
                    info      : false,
                    paging    : false,
                    ordering  : true
                });
            }
        });
        
        // Trigger the `shown.bs.tab` event for the torneos tab if it's active on page load
        if ($('a[href="#default-tab-2"]').hasClass('active')) {
            $('a[href="#default-tab-2"]').trigger('shown.bs.tab');
        }
    });
</script>
<?php #endregion JS datatable ?>
<?php #region region JS focus text input when modal shown ?>
<script>
    focusTextWhenModalShown('#mdl_eliminar_partidosporrevisar_hasta', '#eliminar_partidosporrevisar_hasta_hora');
</script>
<?php #endregion JS focus text input when modal shown ?>
<?php #region region JS countdown functionality ?>
<script type="text/javascript">
    function updateCountdowns() {
        // Get all countdown elements
        const countdownElements = document.querySelectorAll('.countdown-display');

        countdownElements.forEach(function(element) {
            const matchDate = element.getAttribute('data-match-date');
            const matchTime = element.getAttribute('data-match-time');
            const countdownText = element.querySelector('.countdown-text');

            if (matchDate && matchTime && countdownText) {
                // Create the target date using Bogotá timezone
                const targetDateStr = matchDate + ' ' + matchTime;
                const targetDate = new Date(targetDateStr);

                // Get current time in Bogotá timezone (UTC-5)
                const now = new Date();
                const bogotaOffset = -5 * 60; // Bogotá is UTC-5
                const utc = now.getTime() + (now.getTimezoneOffset() * 60000);
                const bogotaTime = new Date(utc + (bogotaOffset * 60000));

                // Calculate the difference
                const timeDiff = targetDate.getTime() - bogotaTime.getTime();

                if (timeDiff > 0) {
                    // Calculate hours and minutes remaining
                    const hours = Math.floor(timeDiff / (1000 * 60 * 60));
                    const minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60));

                    if (hours > 0) {
                        countdownText.textContent = hours + 'h ' + minutes + 'm remaining';
                    } else if (minutes > 0) {
                        countdownText.textContent = minutes + 'm remaining';
                    } else {
                        countdownText.textContent = 'Starting soon';
                    }

                    // Add different styling based on time remaining
                    element.className = element.className.replace(/text-(success|warning|danger|info)/, '');
                    if (hours < 1) {
                        element.classList.add('text-danger');
                    } else if (hours < 3) {
                        element.classList.add('text-warning');
                    } else {
                        element.classList.add('text-info');
                    }
                } else {
                    // Match has started or passed
                    countdownText.textContent = 'Match started';
                    element.className = element.className.replace(/text-(success|warning|danger|info)/, '');
                    element.classList.add('text-success');
                }
            }
        });
    }

    // Update countdowns on page load
    $(document).ready(function() {
        updateCountdowns();

        // Update countdowns every minute
        setInterval(updateCountdowns, 60000);
    });
</script>
<?php #endregion JS countdown functionality ?>
<?php #region region JS autocomplete torneo ?>
<script src="<?php echo RUTA ?>resources/choices.js/choices.min.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function () {
        const torneoSelect = document.getElementById('torneo');
        if (torneoSelect) {
            new Choices(torneoSelect, {
                searchEnabled: true,
                shouldSort: false,
                placeholder: true,
                itemSelectText: ''
            });
        }
    });
</script>
<?php #endregion JS autocomplete torneo ?>

<?php #endregion JS ?>

</body>
</html>